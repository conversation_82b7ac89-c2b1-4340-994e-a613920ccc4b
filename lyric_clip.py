"""
LyricClip - 歌词视频片段容器

使用单一VideoClip容器管理所有歌词时间轴，
通过frame_function统一渲染，复用现有业务逻辑
"""

import numpy as np
from typing import List, Tuple
from moviepy import VideoClip

from lyric_timeline import LyricTimeline
from layout_engine import LayoutEngine
from layout_types import LyricRect


class LyricClip(VideoClip):
    """歌词视频片段容器

    复用现有的enhanced_generator业务逻辑，
    只改变渲染管道：从多ImageClip改为单VideoClip
    """

    def __init__(self,
                 timelines: List[LyricTimeline],
                 layout_engine: LayoutEngine,
                 size: Tuple[int, int],
                 duration: float,
                 fps: float = 30):
        """初始化LyricClip

        Args:
            timelines: 歌词时间轴列表
            layout_engine: 布局引擎
            size: 视频尺寸 (width, height)
            duration: 视频时长
            fps: 帧率
        """
        self.timelines = timelines
        self.layout_engine = layout_engine
        self.video_size = size
        self.fps = fps

        # 创建enhanced_generator实例，复用其业务逻辑
        from enhanced_generator import EnhancedJingwuGenerator
        self.generator = EnhancedJingwuGenerator(size[0], size[1], int(fps))

        # 预计算布局
        self.layout_result = layout_engine.calculate_layout(size[0], size[1])

        # 创建时间轴到布局位置的映射
        self._timeline_positions = {}
        for timeline in timelines:
            if timeline.element_id in self.layout_result.element_positions:
                self._timeline_positions[timeline.element_id] = self.layout_result.element_positions[timeline.element_id]

        # 初始化VideoClip，使用frame_function
        super().__init__(
            frame_function=self._render_frame,
            duration=duration,
            has_constant_size=True
        )
        self.size = size
        self.fps = fps

    def _render_frame(self, t: float) -> np.ndarray:
        """核心渲染方法：在时间t渲染完整的歌词帧

        复用现有的enhanced_generator业务逻辑，只改变输出方式

        Args:
            t: 当前时间

        Returns:
            渲染的帧数据 (height, width, 3)
        """
        # 创建空白画布 (透明背景)
        frame = np.zeros((self.video_size[1], self.video_size[0], 4), dtype=np.uint8)

        # 遍历所有时间轴，渲染当前时间的歌词
        for timeline in self.timelines:
            self._render_timeline_at_time(frame, timeline, t)

        # 转换为RGB格式（MoviePy期望的格式）
        rgb_frame = frame[:, :, :3]  # 去掉alpha通道
        return rgb_frame

    def _render_timeline_at_time(self, frame: np.ndarray,
                               timeline: LyricTimeline,
                               t: float):
        """渲染指定时间轴在当前时间的内容

        复用现有的LyricDisplayStrategy业务逻辑，只改变输出方式

        Args:
            frame: 目标帧数组
            timeline: 歌词时间轴
            t: 当前时间
        """
        # 获取时间轴的布局位置
        if timeline.element_id not in self._timeline_positions:
            return

        layout_rect = self._timeline_positions[timeline.element_id]

        # 复用现有的业务逻辑：使用timeline的get_content_at_time方法
        # 这个方法内部使用了正确的时长计算逻辑
        lyric_content = timeline.get_content_at_time(t)
        if not lyric_content:
            return

        # 复用enhanced_generator的create_enhanced_text_image逻辑
        self._render_using_enhanced_generator(frame, lyric_content, timeline, layout_rect)

    def _render_using_enhanced_generator(self, frame: np.ndarray,
                                       lyric_content: dict,
                                       timeline: LyricTimeline,
                                       layout_rect: LyricRect):
        """复用enhanced_generator的create_enhanced_text_image逻辑

        Args:
            frame: 目标帧数组
            lyric_content: 歌词内容字典 {'text': str, 'start_time': float, 'duration': float, 'index': int}
            timeline: 歌词时间轴
            layout_rect: 布局区域
        """
        # 复用enhanced_generator的create_enhanced_text_image方法
        # 但不创建ImageClip，而是直接获取PIL图像
        text_img_array = self.generator.create_enhanced_text_image(
            text=lyric_content['text'],
            font_size=timeline.style.font_size,
            color=getattr(timeline.style, 'highlight_color', '#FFD700'),  # 使用字符串格式
            width=self.video_size[0],
            height=self.video_size[1],
            y_position=layout_rect.y + layout_rect.height // 2,
            glow=getattr(timeline.style, 'glow_enabled', True)
        )

        if text_img_array is None:
            return

        # 将文本图像合成到主帧上
        self._composite_text_on_frame(frame, text_img_array, layout_rect)

    def _composite_text_on_frame(self, frame: np.ndarray,
                               text_img: np.ndarray,
                               layout_rect: LyricRect):
        """将文本图像合成到主帧上

        Args:
            frame: 目标帧数组
            text_img: 文本图像数组
            layout_rect: 布局区域
        """
        # 计算文本在帧中的位置
        text_height, text_width = text_img.shape[:2]

        # 使用布局位置进行居中对齐
        x = layout_rect.x + (layout_rect.width - text_width) // 2
        y = layout_rect.y + (layout_rect.height - text_height) // 2

        # 确保位置在帧范围内
        x = max(0, min(x, frame.shape[1] - text_width))
        y = max(0, min(y, frame.shape[0] - text_height))

        # 执行alpha合成
        if text_img.shape[2] == 4:  # RGBA
            self._alpha_composite(frame, text_img, x, y)
        else:  # RGB
            frame[y:y+text_height, x:x+text_width, :3] = text_img





    def _alpha_composite(self, background: np.ndarray,
                        foreground: np.ndarray,
                        x: int, y: int):
        """执行alpha合成

        Args:
            background: 背景图像
            foreground: 前景图像（带alpha通道）
            x, y: 前景图像在背景中的位置
        """
        fg_height, fg_width = foreground.shape[:2]

        # 确保不超出边界
        end_y = min(y + fg_height, background.shape[0])
        end_x = min(x + fg_width, background.shape[1])
        actual_height = end_y - y
        actual_width = end_x - x

        if actual_height <= 0 or actual_width <= 0:
            return

        # 获取区域
        bg_region = background[y:end_y, x:end_x]
        fg_region = foreground[:actual_height, :actual_width]

        # 提取alpha通道
        alpha = fg_region[:, :, 3:4].astype(np.float32) / 255.0

        # 执行合成
        bg_region[:, :, :3] = (
            bg_region[:, :, :3].astype(np.float32) * (1 - alpha) +
            fg_region[:, :, :3].astype(np.float32) * alpha
        ).astype(np.uint8)


def create_lyric_clip(timelines: List[LyricTimeline],
                     layout_engine: LayoutEngine,
                     size: Tuple[int, int],
                     duration: float,
                     fps: float = 30) -> LyricClip:
    """创建LyricClip的工厂函数

    Args:
        timelines: 歌词时间轴列表
        layout_engine: 布局引擎
        size: 视频尺寸
        duration: 视频时长
        fps: 帧率

    Returns:
        LyricClip实例
    """
    return LyricClip(timelines, layout_engine, size, duration, fps)
